import dayjs from "dayjs";
import { Row, Col, Space, Statistic, Input } from "antd";
import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { clientUrls } from "misc/urls";
import { useSubfleets } from "misc/api/fleetconfiguration";
import { TextField, NumberField, DateField, TitleField } from "misc/fields";
import { <PERSON>umn<PERSON><PERSON>, ScatterChart } from "misc/charts";
import { Table } from "misc/tables";
import { RangeSelector } from "misc/selectors";

import SideHeader from "navigation/SideHeader";
import ModuleSettings from "modules/ModuleSettings";
import { TABLE_VIEW_ITEMS } from "modules/ModuleSettings";

import mockData from "./data.json";

const VIEW_ITEMS = [...TABLE_VIEW_ITEMS];

const BmDashboardComponentUnify = () => {
    const [t] = useTranslation();
    const { subfleets } = useSubfleets({ suspense: true });

    const { params, setParams } = useParams({
        options: [
            { name: "fleet", persist: "global", allowed: value => subfleets.find(fleet => fleet.subfleetId === value) },
            { name: "range", defaultValue: [dayjs().subtract(30, "days").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")] }
        ]
    });

    // Search state for table filtering
    const [searchValue, setSearchValue] = useState("");

    const fleet = useMemo(() => subfleets.find(fleet => fleet.subfleetId === params.fleet), [params.fleet, subfleets]);

    // Custom date ranges for the RangeSelector
    const customRanges = [
        "lastWeek",
        "lastTwoWeeks",
        "lastFourWeeks",
        {
            label: "t_last_3_months",
            value: [dayjs().subtract(3, "months"), dayjs()]
        },
        {
            label: "t_last_6_months",
            value: [dayjs().subtract(6, "months"), dayjs()]
        }
    ];

    const [selectedRange, setSelectedRange] = useState(params.range);

    // Process SOH data for histogram (exact same as BmSohComponent)
    const sohHistogramData = useMemo(() => {
        const sohValues = mockData.batteryData.map(battery => battery.currentSoh);
        const minSoh = Math.min(...sohValues);
        const maxSoh = Math.max(...sohValues);

        // Create bins with 5% intervals
        const binSize = 5;
        const startRange = Math.floor(minSoh / binSize) * binSize;
        const endRange = Math.ceil(maxSoh / binSize) * binSize;

        const bins = [];
        for (let i = startRange; i <= endRange; i += binSize) {
            bins.push({
                range: `${i}-${i + binSize - 1}%`,
                minValue: i,
                maxValue: i + binSize - 1,
                count: 0
            });
        }

        // Count batteries in each bin
        sohValues.forEach(soh => {
            const binIndex = Math.floor((soh - startRange) / binSize);
            if (bins[binIndex]) {
                bins[binIndex].count++;
            }
        });

        return bins.filter(bin => bin.count > 0); // Only show bins with data
    }, []);

    // Calculate actual SOH statistics from data (exact same as BmSohComponent)
    const sohStatistics = useMemo(() => {
        const sohValues = mockData.batteryData.map(battery => battery.currentSoh);
        const minSoh = Math.min(...sohValues);
        const maxSoh = Math.max(...sohValues);
        const avgSoh = sohValues.reduce((sum, soh) => sum + soh, 0) / sohValues.length;

        return {
            minimum: minSoh,
            average: avgSoh,
            maximum: maxSoh
        };
    }, []);

    // Calculate Safety Score statistics
    const safetyStats = useMemo(() => {
        const safetyValues = mockData.batteryData.map(battery => battery.safetyScore);
        const minSafety = Math.min(...safetyValues);
        const maxSafety = Math.max(...safetyValues);
        const avgSafety = safetyValues.reduce((sum, score) => sum + score, 0) / safetyValues.length;

        return {
            minimum: minSafety,
            average: avgSafety,
            maximum: maxSafety
        };
    }, []);

    // Process Safety Score data for histogram (same logic as BmSafetyScoreComponent)
    const safetyScoreHistogramData = useMemo(() => {
        const safetyValues = mockData.batteryData.map(battery => battery.safetyScore);
        const minScore = Math.min(...safetyValues);
        const maxScore = Math.max(...safetyValues);

        // Create bins with 1-unit intervals for safety scores (0-1, 1-2, 2-3, 3-4, 4-5)
        const binSize = 1;
        const startRange = Math.floor(minScore / binSize) * binSize;
        const endRange = Math.ceil(maxScore / binSize) * binSize;

        const bins = [];
        for (let i = startRange; i <= endRange; i += binSize) {
            const nextBin = i + binSize;
            bins.push({
                range: `${i}-${nextBin}`,
                minValue: i,
                maxValue: nextBin,
                count: 0
            });
        }

        // Count batteries in each bin
        safetyValues.forEach(score => {
            const binIndex = Math.floor((score - startRange) / binSize);
            if (bins[binIndex]) {
                bins[binIndex].count++;
            }
        });

        return bins.filter(bin => bin.count > 0); // Only show bins with data
    }, []);

    // Process data for SOH Degradation Pattern scatterplot
    const scatterplotData = useMemo(() => {
        return mockData.batteryData.map(battery => ({
            batteryAge: battery.batteryAge,
            currentSoh: battery.currentSoh,
            batteryId: battery.batteryId,
            safetyScore: battery.safetyScore,
            totalThroughput: battery.totalThroughput
        }));
    }, []);

    // Filter table data based on search value
    const filteredTableData = useMemo(() => {
        if (!searchValue) return mockData.batteryData;

        const searchLower = searchValue.toLowerCase();
        return mockData.batteryData.filter(
            battery =>
                battery.batteryId?.toLowerCase().includes(searchLower) ||
                battery.currentVin?.toLowerCase().includes(searchLower) ||
                battery.nominalCapacity?.toString().includes(searchLower) ||
                battery.batteryAge?.toString().includes(searchLower) ||
                battery.totalThroughput?.toString().includes(searchLower) ||
                battery.currentSoh?.toString().includes(searchLower) ||
                battery.safetyScore?.toString().includes(searchLower)
        );
    }, [searchValue]);

    // Table columns configuration
    const tableColumns = [
        {
            title: <TextField value="t_battery_id" />,
            dataIndex: "batteryId",
            key: "batteryId",
            sorter: (a, b) => a.batteryId.localeCompare(b.batteryId),
            fixed: "left"
        },
        {
            title: <TextField value="t_battery_type" />,
            dataIndex: "type",
            key: "type",
            render: () => "Li-Ion" // Mock data doesn't have type
        },
        {
            title: <TextField value="t_vin" />,
            dataIndex: "currentVin",
            key: "currentVin"
        },
        {
            title: <TextField value="t_nominal_capacity" />,
            dataIndex: "nominalCapacity",
            key: "nominalCapacity",
            render: value => <NumberField value={value} decimals={1} suffix=" kWh" />,
            sorter: (a, b) => a.nominalCapacity - b.nominalCapacity
        },
        {
            title: <TextField value="t_battery_age" />,
            dataIndex: "batteryAge",
            key: "batteryAge",
            render: value => <NumberField value={value} decimals={1} suffix=" years" />,
            sorter: (a, b) => a.batteryAge - b.batteryAge
        },
        {
            title: <TextField value="t_total_mileage" />,
            dataIndex: "totalThroughput",
            key: "totalThroughput",
            render: value => <NumberField value={value * 1000} decimals={0} suffix=" km" />,
            sorter: (a, b) => a.totalThroughput - b.totalThroughput,
            minWidth: 140
        },
        {
            title: <TextField value="t_soh" />,
            dataIndex: "currentSoh",
            key: "currentSoh",
            render: value => <NumberField value={value} decimals={1} suffix="%" />,
            sorter: (a, b) => a.currentSoh - b.currentSoh,
            minWidth: 80
        },
        {
            title: <TextField value="t_safety_score" />,
            dataIndex: "safetyScore",
            key: "safetyScore",
            render: value => <NumberField value={value} decimals={1} />,
            sorter: (a, b) => a.safetyScore - b.safetyScore
        }
    ];

    // Expandable row render function for installation history
    const renderExpandedRow = record => {
        const historyColumns = [
            {
                title: <TextField value="t_vin" />,
                dataIndex: "vin",
                key: "vin"
            },
            {
                title: <TextField value="t_license_plate" />,
                dataIndex: "licensePlate",
                key: "licensePlate"
            },
            {
                title: <TextField value="t_installation_date" />,
                dataIndex: "installationDate",
                key: "installationDate",
                render: date => <DateField value={date} format="DD.MM.YYYY" />
            },
            {
                title: <TextField value="t_removal_date" />,
                dataIndex: "removalDate",
                key: "removalDate",
                render: date => date ?? <DateField value={date} format="DD.MM.YYYY" />
            },
            {
                title: <TextField value="t_duration" />,
                dataIndex: "durationDays",
                key: "durationDays",
                render: days => <NumberField value={days} decimals={0} suffix=" days" />
            },
            {
                title: <TextField value="t_mileage" />,
                dataIndex: "kilometersInVehicle",
                key: "kilometersInVehicle",
                render: km => <NumberField value={km} decimals={0} suffix=" km" />
            },
            {
                title: <TextField value="t_organisational_unit" />,
                dataIndex: "organisationalUnit",
                key: "organisationalUnit"
            },
            {
                title: <TextField value="t_model" />,
                dataIndex: "model",
                key: "model"
            }
        ];

        return (
            <Row align="start" gutter={[10, 10]}>
                <Col span={24}>
                    <Card>
                        <TitleField value="t_battery_installation_history" level={5} />
                        <Table
                            dataSource={record.installationHistory?.map((item, index) => ({ ...item, key: index })) || []}
                            columns={historyColumns}
                            size="small"
                            pagination={false}
                            scroll={{ x: "100%" }}
                            enumerate={false}
                            stretchWidth={false}
                            bordered
                            style={{ paddingTop: 5 }}
                        />
                    </Card>
                </Col>
            </Row>
        );
    };

    return (
        <SideHeader>
            <Row gutter={[10, 10]}>
                <Col span={24}>
                    <ModuleSettings
                        values={params}
                        viewOptions={{ items: VIEW_ITEMS }}
                        fleetOptions={{ redirectUrl: clientUrls.modules.vlv.fleetConfiguration() }}
                        onChange={values => setParams({ ...params, ...values })}
                    />
                </Col>

                {fleet ? (
                    <Col span={24}>
                        <Card
                            title="Battery Management Dashboard"
                            extra={
                                <RangeSelector
                                    value={selectedRange}
                                    onChange={setSelectedRange}
                                    ranges={customRanges}
                                    showTime={false}
                                    allowClear={false}
                                />
                            }
                        >
                            <Space direction="vertical" size={24} style={{ width: "100%" }}>
                                {/* First Row: SOH Distribution + SOH Degradation Pattern */}
                                <Row gutter={[16, 16]}>
                                    <Col xs={24} lg={12}>
                                        <Card title="SOH Distribution" height={416} size="small">
                                            <ColumnChart
                                                chartConfig={{
                                                    data: sohHistogramData,
                                                    xField: "range",
                                                    yField: "count",
                                                    height: 350,
                                                    xAxis: {
                                                        title: {
                                                            text: "SOH Range (%)"
                                                        }
                                                    },
                                                    yAxis: {
                                                        title: {
                                                            text: "Number of Batteries"
                                                        }
                                                    },
                                                    loading: false
                                                }}
                                            />
                                        </Card>
                                    </Col>
                                    <Col xs={24} lg={12}>
                                        <Card title="SOH Degradation" height={416} size="small">
                                            <ScatterChart
                                                chartConfig={{
                                                    data: scatterplotData,
                                                    xField: "batteryAge",
                                                    yField: "currentSoh",
                                                    height: 350,
                                                    xAxis: {
                                                        title: {
                                                            text: "Battery Age (years)"
                                                        },
                                                        min: 0,
                                                        max: 10,
                                                        tickInterval: 2
                                                    },
                                                    yAxis: {
                                                        title: {
                                                            text: "SOH (%)"
                                                        },
                                                        min: 70,
                                                        max: 100,
                                                        tickInterval: 5
                                                    },
                                                    tooltip: {
                                                        fields: ["batteryId", "batteryAge", "currentSoh", "safetyScore", "totalThroughput"],
                                                        formatter: datum => ({
                                                            name: datum.batteryId,
                                                            value: `Age: ${datum.batteryAge} years, SOH: ${datum.currentSoh}%, Safety: ${
                                                                datum.safetyScore
                                                            }, Km: ${(datum.totalThroughput * 1000).toLocaleString()} km`
                                                        })
                                                    },
                                                    annotations: [
                                                        {
                                                            type: "line",
                                                            start: ["min", 80],
                                                            end: ["max", 80],
                                                            style: {
                                                                stroke: "#ff4d4f",
                                                                lineWidth: 2,
                                                                lineDash: [4, 4]
                                                            },
                                                            text: {
                                                                content: "End of Life",
                                                                position: "end",
                                                                offsetX: -10,
                                                                offsetY: -5,
                                                                style: {
                                                                    fill: "#ff4d4f",
                                                                    fontSize: 10,
                                                                    textAlign: "right"
                                                                }
                                                            }
                                                        }
                                                    ],
                                                    loading: false
                                                }}
                                            />
                                        </Card>
                                    </Col>
                                </Row>

                                {/* Second Row: SOH & Safety Score Stats (left) + Safety Score Distribution (right) */}
                                <Row gutter={[16, 16]}>
                                    <Col xs={24} lg={12}>
                                        <Card title="Statistics" height={416} size="small">
                                            <Row gutter={[16, 16]} style={{ height: "100%" }}>
                                                {/* SOH Statistics */}
                                                <Col span={24}>
                                                    <div style={{ marginBottom: 16 }}>
                                                        <h4 style={{ margin: "0 0 12px 0", fontSize: "14px", fontWeight: 600 }}>SOH</h4>
                                                        <Row gutter={[12, 12]}>
                                                            <Col span={8}>
                                                                <div
                                                                    style={{
                                                                        textAlign: "center",
                                                                        padding: "8px",
                                                                        border: "1px solid #f0f0f0",
                                                                        borderRadius: "6px"
                                                                    }}
                                                                >
                                                                    <Statistic
                                                                        title="Min SOH"
                                                                        value={sohStatistics.minimum}
                                                                        suffix="%"
                                                                        precision={1}
                                                                        valueStyle={{ fontSize: "16px" }}
                                                                    />
                                                                </div>
                                                            </Col>
                                                            <Col span={8}>
                                                                <div
                                                                    style={{
                                                                        textAlign: "center",
                                                                        padding: "8px",
                                                                        border: "1px solid #f0f0f0",
                                                                        borderRadius: "6px"
                                                                    }}
                                                                >
                                                                    <Statistic
                                                                        title="Avg SOH"
                                                                        value={sohStatistics.average}
                                                                        suffix="%"
                                                                        precision={1}
                                                                        valueStyle={{ fontSize: "16px" }}
                                                                    />
                                                                </div>
                                                            </Col>
                                                            <Col span={8}>
                                                                <div
                                                                    style={{
                                                                        textAlign: "center",
                                                                        padding: "8px",
                                                                        border: "1px solid #f0f0f0",
                                                                        borderRadius: "6px"
                                                                    }}
                                                                >
                                                                    <Statistic
                                                                        title="Max SOH"
                                                                        value={sohStatistics.maximum}
                                                                        suffix="%"
                                                                        precision={1}
                                                                        valueStyle={{ fontSize: "16px" }}
                                                                    />
                                                                </div>
                                                            </Col>
                                                        </Row>
                                                    </div>
                                                </Col>

                                                {/* Safety Score Statistics */}
                                                <Col span={24}>
                                                    <div>
                                                        <h4 style={{ margin: "0 0 12px 0", fontSize: "14px", fontWeight: 600 }}>Safety Score</h4>
                                                        <Row gutter={[12, 12]}>
                                                            <Col span={8}>
                                                                <div
                                                                    style={{
                                                                        textAlign: "center",
                                                                        padding: "8px",
                                                                        border: "1px solid #f0f0f0",
                                                                        borderRadius: "6px"
                                                                    }}
                                                                >
                                                                    <Statistic
                                                                        title="Min Safety"
                                                                        value={safetyStats.minimum}
                                                                        precision={1}
                                                                        valueStyle={{ fontSize: "16px" }}
                                                                    />
                                                                </div>
                                                            </Col>
                                                            <Col span={8}>
                                                                <div
                                                                    style={{
                                                                        textAlign: "center",
                                                                        padding: "8px",
                                                                        border: "1px solid #f0f0f0",
                                                                        borderRadius: "6px"
                                                                    }}
                                                                >
                                                                    <Statistic
                                                                        title="Avg Safety"
                                                                        value={safetyStats.average}
                                                                        precision={1}
                                                                        valueStyle={{ fontSize: "16px" }}
                                                                    />
                                                                </div>
                                                            </Col>
                                                            <Col span={8}>
                                                                <div
                                                                    style={{
                                                                        textAlign: "center",
                                                                        padding: "8px",
                                                                        border: "1px solid #f0f0f0",
                                                                        borderRadius: "6px"
                                                                    }}
                                                                >
                                                                    <Statistic
                                                                        title="Max Safety"
                                                                        value={safetyStats.maximum}
                                                                        precision={1}
                                                                        valueStyle={{ fontSize: "16px" }}
                                                                    />
                                                                </div>
                                                            </Col>
                                                        </Row>
                                                    </div>
                                                </Col>
                                            </Row>
                                        </Card>
                                    </Col>
                                    <Col xs={24} lg={12}>
                                        <Card title="Safety Score Distribution" height={416} size="small">
                                            <ColumnChart
                                                chartConfig={{
                                                    data: safetyScoreHistogramData,
                                                    xField: "range",
                                                    yField: "count",
                                                    height: 350,
                                                    xAxis: {
                                                        title: {
                                                            text: "Safety Score Range"
                                                        }
                                                    },
                                                    yAxis: {
                                                        title: {
                                                            text: "Number of Batteries"
                                                        }
                                                    },
                                                    loading: false
                                                }}
                                            />
                                        </Card>
                                    </Col>
                                </Row>

                                {/* Battery Data Table with Search */}
                                <Card title="Battery" size="small">
                                    <Space direction="vertical" size={20} style={{ width: "100%" }}>
                                        <Row justify="space-between">
                                            <Input.Search
                                                onSearch={value => setSearchValue(value)}
                                                onChange={e => setSearchValue(e.target.value)}
                                                placeholder={t("t_search")}
                                                style={{ width: 350 }}
                                                allowClear
                                                value={searchValue}
                                            />
                                        </Row>
                                        <Table
                                            dataSource={filteredTableData}
                                            columns={tableColumns}
                                            expandable={{
                                                expandedRowRender: renderExpandedRow,
                                                rowExpandable: record => !!record.installationHistory?.length
                                            }}
                                            rowKey="key"
                                            scroll={{ x: "max-content" }}
                                            size="small"
                                            pagination={{
                                                pageSize: 20,
                                                showSizeChanger: true,
                                                showTotal: (total, range) => <TextField prefix={range.join("-")} value="t_of" suffix={total} />
                                            }}
                                        />
                                    </Space>
                                </Card>
                            </Space>
                        </Card>
                    </Col>
                ) : (
                    <Col span={24}>
                        <Card height={500}>
                            <Result type="noFleetSelected" url={clientUrls.modules.bm.dashboard()} />
                        </Card>
                    </Col>
                )}
            </Row>
        </SideHeader>
    );
};

export default BmDashboardComponentUnify;
